// 配置文件
module.exports = {
  // WebIDE URL
  webideUrl: 'https://3e8ccf585a6c4fbd9f1aa9f05ac5e415.ap-shanghai.cloudstudio.club/?mode=edit',
  
  // Cookie文件路径
  cookieFile: './cookies.json',
  
  // 浏览器配置
  browserOptions: {
    headless: false, // 设置为true可以无头模式运行
    slowMo: 100,     // 操作间隔时间（毫秒）
    timeout: 30000   // 超时时间（毫秒）
  },
  
  // 要执行的命令
  command: 'service cron start',
  
  // 等待时间配置（毫秒）
  waitTimes: {
    pageLoad: 5000,      // 页面加载等待时间
    terminalOpen: 3000,  // 终端打开等待时间
    commandExecution: 2000 // 命令执行等待时间
  },
  
  // 登录页面选择器（需要根据实际登录页面调整）
  selectors: {
    // 这些选择器需要根据实际的登录页面进行调整
    usernameInput: 'input[type="email"], input[name="username"], input[name="email"]',
    passwordInput: 'input[type="password"], input[name="password"]',
    loginButton: 'button[type="submit"], input[type="submit"], .login-btn, .btn-login',
    terminal: '.terminal, .xterm, .console',
    terminalInput: '.terminal input, .xterm-helper-textarea, .console input'
  }
};

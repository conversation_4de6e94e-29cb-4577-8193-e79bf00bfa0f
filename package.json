{"name": "cloudstudio-runner", "version": "1.0.0", "description": "Playwright automation for CloudStudio WebIDE", "main": "index.js", "scripts": {"login": "node login.js", "execute": "node execute-command.js", "schedule": "node scheduler.js", "schedule:30min": "node scheduler.js --interval 30", "schedule:1hour": "node scheduler.js --interval 60", "test-connection": "node test-connection.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["playwright", "automation", "webide"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"playwright": "^1.52.0"}}
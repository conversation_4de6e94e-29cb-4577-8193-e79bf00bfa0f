const { chromium } = require('playwright');
const config = require('./config');

async function testConnection() {
  console.log('测试连接到 WebIDE...');
  
  const browser = await chromium.launch({
    ...config.browserOptions,
    headless: false // 强制显示浏览器以便观察
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log(`正在访问: ${config.webideUrl}`);
    await page.goto(config.webideUrl);
    
    // 等待页面加载
    await page.waitForTimeout(3000);
    
    console.log('页面信息:');
    console.log('- URL:', page.url());
    console.log('- 标题:', await page.title());
    
    // 检查页面内容
    const bodyText = await page.textContent('body');
    console.log('- 页面是否包含登录相关内容:', 
      bodyText.toLowerCase().includes('login') || 
      bodyText.toLowerCase().includes('sign in') ||
      bodyText.toLowerCase().includes('登录')
    );
    
    console.log('- 页面是否包含编辑器相关内容:', 
      bodyText.toLowerCase().includes('editor') || 
      bodyText.toLowerCase().includes('monaco') ||
      bodyText.toLowerCase().includes('ide')
    );
    
    // 检查常见元素
    const elements = {
      'Monaco编辑器': '.monaco-editor',
      '终端': '.terminal, .xterm',
      '登录表单': 'form, input[type="password"]',
      '按钮': 'button'
    };
    
    console.log('\n页面元素检查:');
    for (const [name, selector] of Object.entries(elements)) {
      const count = await page.locator(selector).count();
      console.log(`- ${name}: ${count > 0 ? '✓ 找到' : '✗ 未找到'} (${count}个)`);
    }
    
    // 保持浏览器打开以便手动检查
    console.log('\n浏览器将保持打开10秒以便检查...');
    await page.waitForTimeout(10000);
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    await browser.close();
    console.log('测试完成');
  }
}

// 运行测试
if (require.main === module) {
  testConnection().catch(console.error);
}

module.exports = { testConnection };
